"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/whatwg-url";
exports.ids = ["vendor-chunks/whatwg-url"];
exports.modules = {

/***/ "(ssr)/./node_modules/whatwg-url/lib/URL-impl.js":
/*!*************************************************!*\
  !*** ./node_modules/whatwg-url/lib/URL-impl.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nconst usm = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\");\nexports.implementation = class URLImpl {\n    constructor(constructorArgs){\n        const url = constructorArgs[0];\n        const base = constructorArgs[1];\n        let parsedBase = null;\n        if (base !== undefined) {\n            parsedBase = usm.basicURLParse(base);\n            if (parsedBase === \"failure\") {\n                throw new TypeError(\"Invalid base URL\");\n            }\n        }\n        const parsedURL = usm.basicURLParse(url, {\n            baseURL: parsedBase\n        });\n        if (parsedURL === \"failure\") {\n            throw new TypeError(\"Invalid URL\");\n        }\n        this._url = parsedURL;\n    // TODO: query stuff\n    }\n    get href() {\n        return usm.serializeURL(this._url);\n    }\n    set href(v) {\n        const parsedURL = usm.basicURLParse(v);\n        if (parsedURL === \"failure\") {\n            throw new TypeError(\"Invalid URL\");\n        }\n        this._url = parsedURL;\n    }\n    get origin() {\n        return usm.serializeURLOrigin(this._url);\n    }\n    get protocol() {\n        return this._url.scheme + \":\";\n    }\n    set protocol(v) {\n        usm.basicURLParse(v + \":\", {\n            url: this._url,\n            stateOverride: \"scheme start\"\n        });\n    }\n    get username() {\n        return this._url.username;\n    }\n    set username(v) {\n        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n            return;\n        }\n        usm.setTheUsername(this._url, v);\n    }\n    get password() {\n        return this._url.password;\n    }\n    set password(v) {\n        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n            return;\n        }\n        usm.setThePassword(this._url, v);\n    }\n    get host() {\n        const url = this._url;\n        if (url.host === null) {\n            return \"\";\n        }\n        if (url.port === null) {\n            return usm.serializeHost(url.host);\n        }\n        return usm.serializeHost(url.host) + \":\" + usm.serializeInteger(url.port);\n    }\n    set host(v) {\n        if (this._url.cannotBeABaseURL) {\n            return;\n        }\n        usm.basicURLParse(v, {\n            url: this._url,\n            stateOverride: \"host\"\n        });\n    }\n    get hostname() {\n        if (this._url.host === null) {\n            return \"\";\n        }\n        return usm.serializeHost(this._url.host);\n    }\n    set hostname(v) {\n        if (this._url.cannotBeABaseURL) {\n            return;\n        }\n        usm.basicURLParse(v, {\n            url: this._url,\n            stateOverride: \"hostname\"\n        });\n    }\n    get port() {\n        if (this._url.port === null) {\n            return \"\";\n        }\n        return usm.serializeInteger(this._url.port);\n    }\n    set port(v) {\n        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n            return;\n        }\n        if (v === \"\") {\n            this._url.port = null;\n        } else {\n            usm.basicURLParse(v, {\n                url: this._url,\n                stateOverride: \"port\"\n            });\n        }\n    }\n    get pathname() {\n        if (this._url.cannotBeABaseURL) {\n            return this._url.path[0];\n        }\n        if (this._url.path.length === 0) {\n            return \"\";\n        }\n        return \"/\" + this._url.path.join(\"/\");\n    }\n    set pathname(v) {\n        if (this._url.cannotBeABaseURL) {\n            return;\n        }\n        this._url.path = [];\n        usm.basicURLParse(v, {\n            url: this._url,\n            stateOverride: \"path start\"\n        });\n    }\n    get search() {\n        if (this._url.query === null || this._url.query === \"\") {\n            return \"\";\n        }\n        return \"?\" + this._url.query;\n    }\n    set search(v) {\n        // TODO: query stuff\n        const url = this._url;\n        if (v === \"\") {\n            url.query = null;\n            return;\n        }\n        const input = v[0] === \"?\" ? v.substring(1) : v;\n        url.query = \"\";\n        usm.basicURLParse(input, {\n            url,\n            stateOverride: \"query\"\n        });\n    }\n    get hash() {\n        if (this._url.fragment === null || this._url.fragment === \"\") {\n            return \"\";\n        }\n        return \"#\" + this._url.fragment;\n    }\n    set hash(v) {\n        if (v === \"\") {\n            this._url.fragment = null;\n            return;\n        }\n        const input = v[0] === \"#\" ? v.substring(1) : v;\n        this._url.fragment = \"\";\n        usm.basicURLParse(input, {\n            url: this._url,\n            stateOverride: \"fragment\"\n        });\n    }\n    toJSON() {\n        return this.href;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/URL-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/whatwg-url/lib/URL.js":
/*!********************************************!*\
  !*** ./node_modules/whatwg-url/lib/URL.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst conversions = __webpack_require__(/*! webidl-conversions */ \"(ssr)/./node_modules/webidl-conversions/lib/index.js\");\nconst utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/whatwg-url/lib/utils.js\");\nconst Impl = __webpack_require__(/*! .//URL-impl.js */ \"(ssr)/./node_modules/whatwg-url/lib/URL-impl.js\");\nconst impl = utils.implSymbol;\nfunction URL(url) {\n    if (!this || this[impl] || !(this instanceof URL)) {\n        throw new TypeError(\"Failed to construct 'URL': Please use the 'new' operator, this DOM object constructor cannot be called as a function.\");\n    }\n    if (arguments.length < 1) {\n        throw new TypeError(\"Failed to construct 'URL': 1 argument required, but only \" + arguments.length + \" present.\");\n    }\n    const args = [];\n    for(let i = 0; i < arguments.length && i < 2; ++i){\n        args[i] = arguments[i];\n    }\n    args[0] = conversions[\"USVString\"](args[0]);\n    if (args[1] !== undefined) {\n        args[1] = conversions[\"USVString\"](args[1]);\n    }\n    module.exports.setup(this, args);\n}\nURL.prototype.toJSON = function toJSON() {\n    if (!this || !module.exports.is(this)) {\n        throw new TypeError(\"Illegal invocation\");\n    }\n    const args = [];\n    for(let i = 0; i < arguments.length && i < 0; ++i){\n        args[i] = arguments[i];\n    }\n    return this[impl].toJSON.apply(this[impl], args);\n};\nObject.defineProperty(URL.prototype, \"href\", {\n    get () {\n        return this[impl].href;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].href = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nURL.prototype.toString = function() {\n    if (!this || !module.exports.is(this)) {\n        throw new TypeError(\"Illegal invocation\");\n    }\n    return this.href;\n};\nObject.defineProperty(URL.prototype, \"origin\", {\n    get () {\n        return this[impl].origin;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"protocol\", {\n    get () {\n        return this[impl].protocol;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].protocol = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"username\", {\n    get () {\n        return this[impl].username;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].username = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"password\", {\n    get () {\n        return this[impl].password;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].password = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"host\", {\n    get () {\n        return this[impl].host;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].host = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"hostname\", {\n    get () {\n        return this[impl].hostname;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].hostname = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"port\", {\n    get () {\n        return this[impl].port;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].port = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"pathname\", {\n    get () {\n        return this[impl].pathname;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].pathname = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"search\", {\n    get () {\n        return this[impl].search;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].search = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"hash\", {\n    get () {\n        return this[impl].hash;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].hash = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nmodule.exports = {\n    is (obj) {\n        return !!obj && obj[impl] instanceof Impl.implementation;\n    },\n    create (constructorArgs, privateData) {\n        let obj = Object.create(URL.prototype);\n        this.setup(obj, constructorArgs, privateData);\n        return obj;\n    },\n    setup (obj, constructorArgs, privateData) {\n        if (!privateData) privateData = {};\n        privateData.wrapper = obj;\n        obj[impl] = new Impl.implementation(constructorArgs, privateData);\n        obj[impl][utils.wrapperSymbol] = obj;\n    },\n    interface: URL,\n    expose: {\n        Window: {\n            URL: URL\n        },\n        Worker: {\n            URL: URL\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/URL.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/whatwg-url/lib/public-api.js":
/*!***************************************************!*\
  !*** ./node_modules/whatwg-url/lib/public-api.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.URL = __webpack_require__(/*! ./URL */ \"(ssr)/./node_modules/whatwg-url/lib/URL.js\")[\"interface\"];\nexports.serializeURL = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeURL;\nexports.serializeURLOrigin = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeURLOrigin;\nexports.basicURLParse = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").basicURLParse;\nexports.setTheUsername = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").setTheUsername;\nexports.setThePassword = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").setThePassword;\nexports.serializeHost = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeHost;\nexports.serializeInteger = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeInteger;\nexports.parseURL = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").parseURL;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2hhdHdnLXVybC9saWIvcHVibGljLWFwaS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViQSx5R0FBd0M7QUFDeENBLDhJQUFrRTtBQUNsRUEsMEpBQThFO0FBQzlFQSxnSkFBb0U7QUFDcEVBLGtKQUFzRTtBQUN0RUEsa0pBQXNFO0FBQ3RFQSxnSkFBb0U7QUFDcEVBLHNKQUEwRTtBQUMxRUEsc0lBQTBEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zaXRpdmU3LXRvdXJpc20td2Vic2l0ZS8uL25vZGVfbW9kdWxlcy93aGF0d2ctdXJsL2xpYi9wdWJsaWMtYXBpLmpzPzJkODQiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmV4cG9ydHMuVVJMID0gcmVxdWlyZShcIi4vVVJMXCIpLmludGVyZmFjZTtcbmV4cG9ydHMuc2VyaWFsaXplVVJMID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuc2VyaWFsaXplVVJMO1xuZXhwb3J0cy5zZXJpYWxpemVVUkxPcmlnaW4gPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5zZXJpYWxpemVVUkxPcmlnaW47XG5leHBvcnRzLmJhc2ljVVJMUGFyc2UgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5iYXNpY1VSTFBhcnNlO1xuZXhwb3J0cy5zZXRUaGVVc2VybmFtZSA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnNldFRoZVVzZXJuYW1lO1xuZXhwb3J0cy5zZXRUaGVQYXNzd29yZCA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnNldFRoZVBhc3N3b3JkO1xuZXhwb3J0cy5zZXJpYWxpemVIb3N0ID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuc2VyaWFsaXplSG9zdDtcbmV4cG9ydHMuc2VyaWFsaXplSW50ZWdlciA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnNlcmlhbGl6ZUludGVnZXI7XG5leHBvcnRzLnBhcnNlVVJMID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikucGFyc2VVUkw7XG4iXSwibmFtZXMiOlsiZXhwb3J0cyIsIlVSTCIsInJlcXVpcmUiLCJpbnRlcmZhY2UiLCJzZXJpYWxpemVVUkwiLCJzZXJpYWxpemVVUkxPcmlnaW4iLCJiYXNpY1VSTFBhcnNlIiwic2V0VGhlVXNlcm5hbWUiLCJzZXRUaGVQYXNzd29yZCIsInNlcmlhbGl6ZUhvc3QiLCJzZXJpYWxpemVJbnRlZ2VyIiwicGFyc2VVUkwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/public-api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js":
/*!**********************************************************!*\
  !*** ./node_modules/whatwg-url/lib/url-state-machine.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst punycode = __webpack_require__(/*! punycode */ \"punycode\");\nconst tr46 = __webpack_require__(/*! tr46 */ \"(ssr)/./node_modules/tr46/index.js\");\nconst specialSchemes = {\n    ftp: 21,\n    file: null,\n    gopher: 70,\n    http: 80,\n    https: 443,\n    ws: 80,\n    wss: 443\n};\nconst failure = Symbol(\"failure\");\nfunction countSymbols(str) {\n    return punycode.ucs2.decode(str).length;\n}\nfunction at(input, idx) {\n    const c = input[idx];\n    return isNaN(c) ? undefined : String.fromCodePoint(c);\n}\nfunction isASCIIDigit(c) {\n    return c >= 0x30 && c <= 0x39;\n}\nfunction isASCIIAlpha(c) {\n    return c >= 0x41 && c <= 0x5A || c >= 0x61 && c <= 0x7A;\n}\nfunction isASCIIAlphanumeric(c) {\n    return isASCIIAlpha(c) || isASCIIDigit(c);\n}\nfunction isASCIIHex(c) {\n    return isASCIIDigit(c) || c >= 0x41 && c <= 0x46 || c >= 0x61 && c <= 0x66;\n}\nfunction isSingleDot(buffer) {\n    return buffer === \".\" || buffer.toLowerCase() === \"%2e\";\n}\nfunction isDoubleDot(buffer) {\n    buffer = buffer.toLowerCase();\n    return buffer === \"..\" || buffer === \"%2e.\" || buffer === \".%2e\" || buffer === \"%2e%2e\";\n}\nfunction isWindowsDriveLetterCodePoints(cp1, cp2) {\n    return isASCIIAlpha(cp1) && (cp2 === 58 || cp2 === 124);\n}\nfunction isWindowsDriveLetterString(string) {\n    return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && (string[1] === \":\" || string[1] === \"|\");\n}\nfunction isNormalizedWindowsDriveLetterString(string) {\n    return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && string[1] === \":\";\n}\nfunction containsForbiddenHostCodePoint(string) {\n    return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|%|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\n}\nfunction containsForbiddenHostCodePointExcludingPercent(string) {\n    return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\n}\nfunction isSpecialScheme(scheme) {\n    return specialSchemes[scheme] !== undefined;\n}\nfunction isSpecial(url) {\n    return isSpecialScheme(url.scheme);\n}\nfunction defaultPort(scheme) {\n    return specialSchemes[scheme];\n}\nfunction percentEncode(c) {\n    let hex = c.toString(16).toUpperCase();\n    if (hex.length === 1) {\n        hex = \"0\" + hex;\n    }\n    return \"%\" + hex;\n}\nfunction utf8PercentEncode(c) {\n    const buf = new Buffer(c);\n    let str = \"\";\n    for(let i = 0; i < buf.length; ++i){\n        str += percentEncode(buf[i]);\n    }\n    return str;\n}\nfunction utf8PercentDecode(str) {\n    const input = new Buffer(str);\n    const output = [];\n    for(let i = 0; i < input.length; ++i){\n        if (input[i] !== 37) {\n            output.push(input[i]);\n        } else if (input[i] === 37 && isASCIIHex(input[i + 1]) && isASCIIHex(input[i + 2])) {\n            output.push(parseInt(input.slice(i + 1, i + 3).toString(), 16));\n            i += 2;\n        } else {\n            output.push(input[i]);\n        }\n    }\n    return new Buffer(output).toString();\n}\nfunction isC0ControlPercentEncode(c) {\n    return c <= 0x1F || c > 0x7E;\n}\nconst extraPathPercentEncodeSet = new Set([\n    32,\n    34,\n    35,\n    60,\n    62,\n    63,\n    96,\n    123,\n    125\n]);\nfunction isPathPercentEncode(c) {\n    return isC0ControlPercentEncode(c) || extraPathPercentEncodeSet.has(c);\n}\nconst extraUserinfoPercentEncodeSet = new Set([\n    47,\n    58,\n    59,\n    61,\n    64,\n    91,\n    92,\n    93,\n    94,\n    124\n]);\nfunction isUserinfoPercentEncode(c) {\n    return isPathPercentEncode(c) || extraUserinfoPercentEncodeSet.has(c);\n}\nfunction percentEncodeChar(c, encodeSetPredicate) {\n    const cStr = String.fromCodePoint(c);\n    if (encodeSetPredicate(c)) {\n        return utf8PercentEncode(cStr);\n    }\n    return cStr;\n}\nfunction parseIPv4Number(input) {\n    let R = 10;\n    if (input.length >= 2 && input.charAt(0) === \"0\" && input.charAt(1).toLowerCase() === \"x\") {\n        input = input.substring(2);\n        R = 16;\n    } else if (input.length >= 2 && input.charAt(0) === \"0\") {\n        input = input.substring(1);\n        R = 8;\n    }\n    if (input === \"\") {\n        return 0;\n    }\n    const regex = R === 10 ? /[^0-9]/ : R === 16 ? /[^0-9A-Fa-f]/ : /[^0-7]/;\n    if (regex.test(input)) {\n        return failure;\n    }\n    return parseInt(input, R);\n}\nfunction parseIPv4(input) {\n    const parts = input.split(\".\");\n    if (parts[parts.length - 1] === \"\") {\n        if (parts.length > 1) {\n            parts.pop();\n        }\n    }\n    if (parts.length > 4) {\n        return input;\n    }\n    const numbers = [];\n    for (const part of parts){\n        if (part === \"\") {\n            return input;\n        }\n        const n = parseIPv4Number(part);\n        if (n === failure) {\n            return input;\n        }\n        numbers.push(n);\n    }\n    for(let i = 0; i < numbers.length - 1; ++i){\n        if (numbers[i] > 255) {\n            return failure;\n        }\n    }\n    if (numbers[numbers.length - 1] >= Math.pow(256, 5 - numbers.length)) {\n        return failure;\n    }\n    let ipv4 = numbers.pop();\n    let counter = 0;\n    for (const n of numbers){\n        ipv4 += n * Math.pow(256, 3 - counter);\n        ++counter;\n    }\n    return ipv4;\n}\nfunction serializeIPv4(address) {\n    let output = \"\";\n    let n = address;\n    for(let i = 1; i <= 4; ++i){\n        output = String(n % 256) + output;\n        if (i !== 4) {\n            output = \".\" + output;\n        }\n        n = Math.floor(n / 256);\n    }\n    return output;\n}\nfunction parseIPv6(input) {\n    const address = [\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0\n    ];\n    let pieceIndex = 0;\n    let compress = null;\n    let pointer = 0;\n    input = punycode.ucs2.decode(input);\n    if (input[pointer] === 58) {\n        if (input[pointer + 1] !== 58) {\n            return failure;\n        }\n        pointer += 2;\n        ++pieceIndex;\n        compress = pieceIndex;\n    }\n    while(pointer < input.length){\n        if (pieceIndex === 8) {\n            return failure;\n        }\n        if (input[pointer] === 58) {\n            if (compress !== null) {\n                return failure;\n            }\n            ++pointer;\n            ++pieceIndex;\n            compress = pieceIndex;\n            continue;\n        }\n        let value = 0;\n        let length = 0;\n        while(length < 4 && isASCIIHex(input[pointer])){\n            value = value * 0x10 + parseInt(at(input, pointer), 16);\n            ++pointer;\n            ++length;\n        }\n        if (input[pointer] === 46) {\n            if (length === 0) {\n                return failure;\n            }\n            pointer -= length;\n            if (pieceIndex > 6) {\n                return failure;\n            }\n            let numbersSeen = 0;\n            while(input[pointer] !== undefined){\n                let ipv4Piece = null;\n                if (numbersSeen > 0) {\n                    if (input[pointer] === 46 && numbersSeen < 4) {\n                        ++pointer;\n                    } else {\n                        return failure;\n                    }\n                }\n                if (!isASCIIDigit(input[pointer])) {\n                    return failure;\n                }\n                while(isASCIIDigit(input[pointer])){\n                    const number = parseInt(at(input, pointer));\n                    if (ipv4Piece === null) {\n                        ipv4Piece = number;\n                    } else if (ipv4Piece === 0) {\n                        return failure;\n                    } else {\n                        ipv4Piece = ipv4Piece * 10 + number;\n                    }\n                    if (ipv4Piece > 255) {\n                        return failure;\n                    }\n                    ++pointer;\n                }\n                address[pieceIndex] = address[pieceIndex] * 0x100 + ipv4Piece;\n                ++numbersSeen;\n                if (numbersSeen === 2 || numbersSeen === 4) {\n                    ++pieceIndex;\n                }\n            }\n            if (numbersSeen !== 4) {\n                return failure;\n            }\n            break;\n        } else if (input[pointer] === 58) {\n            ++pointer;\n            if (input[pointer] === undefined) {\n                return failure;\n            }\n        } else if (input[pointer] !== undefined) {\n            return failure;\n        }\n        address[pieceIndex] = value;\n        ++pieceIndex;\n    }\n    if (compress !== null) {\n        let swaps = pieceIndex - compress;\n        pieceIndex = 7;\n        while(pieceIndex !== 0 && swaps > 0){\n            const temp = address[compress + swaps - 1];\n            address[compress + swaps - 1] = address[pieceIndex];\n            address[pieceIndex] = temp;\n            --pieceIndex;\n            --swaps;\n        }\n    } else if (compress === null && pieceIndex !== 8) {\n        return failure;\n    }\n    return address;\n}\nfunction serializeIPv6(address) {\n    let output = \"\";\n    const seqResult = findLongestZeroSequence(address);\n    const compress = seqResult.idx;\n    let ignore0 = false;\n    for(let pieceIndex = 0; pieceIndex <= 7; ++pieceIndex){\n        if (ignore0 && address[pieceIndex] === 0) {\n            continue;\n        } else if (ignore0) {\n            ignore0 = false;\n        }\n        if (compress === pieceIndex) {\n            const separator = pieceIndex === 0 ? \"::\" : \":\";\n            output += separator;\n            ignore0 = true;\n            continue;\n        }\n        output += address[pieceIndex].toString(16);\n        if (pieceIndex !== 7) {\n            output += \":\";\n        }\n    }\n    return output;\n}\nfunction parseHost(input, isSpecialArg) {\n    if (input[0] === \"[\") {\n        if (input[input.length - 1] !== \"]\") {\n            return failure;\n        }\n        return parseIPv6(input.substring(1, input.length - 1));\n    }\n    if (!isSpecialArg) {\n        return parseOpaqueHost(input);\n    }\n    const domain = utf8PercentDecode(input);\n    const asciiDomain = tr46.toASCII(domain, false, tr46.PROCESSING_OPTIONS.NONTRANSITIONAL, false);\n    if (asciiDomain === null) {\n        return failure;\n    }\n    if (containsForbiddenHostCodePoint(asciiDomain)) {\n        return failure;\n    }\n    const ipv4Host = parseIPv4(asciiDomain);\n    if (typeof ipv4Host === \"number\" || ipv4Host === failure) {\n        return ipv4Host;\n    }\n    return asciiDomain;\n}\nfunction parseOpaqueHost(input) {\n    if (containsForbiddenHostCodePointExcludingPercent(input)) {\n        return failure;\n    }\n    let output = \"\";\n    const decoded = punycode.ucs2.decode(input);\n    for(let i = 0; i < decoded.length; ++i){\n        output += percentEncodeChar(decoded[i], isC0ControlPercentEncode);\n    }\n    return output;\n}\nfunction findLongestZeroSequence(arr) {\n    let maxIdx = null;\n    let maxLen = 1; // only find elements > 1\n    let currStart = null;\n    let currLen = 0;\n    for(let i = 0; i < arr.length; ++i){\n        if (arr[i] !== 0) {\n            if (currLen > maxLen) {\n                maxIdx = currStart;\n                maxLen = currLen;\n            }\n            currStart = null;\n            currLen = 0;\n        } else {\n            if (currStart === null) {\n                currStart = i;\n            }\n            ++currLen;\n        }\n    }\n    // if trailing zeros\n    if (currLen > maxLen) {\n        maxIdx = currStart;\n        maxLen = currLen;\n    }\n    return {\n        idx: maxIdx,\n        len: maxLen\n    };\n}\nfunction serializeHost(host) {\n    if (typeof host === \"number\") {\n        return serializeIPv4(host);\n    }\n    // IPv6 serializer\n    if (host instanceof Array) {\n        return \"[\" + serializeIPv6(host) + \"]\";\n    }\n    return host;\n}\nfunction trimControlChars(url) {\n    return url.replace(/^[\\u0000-\\u001F\\u0020]+|[\\u0000-\\u001F\\u0020]+$/g, \"\");\n}\nfunction trimTabAndNewline(url) {\n    return url.replace(/\\u0009|\\u000A|\\u000D/g, \"\");\n}\nfunction shortenPath(url) {\n    const path = url.path;\n    if (path.length === 0) {\n        return;\n    }\n    if (url.scheme === \"file\" && path.length === 1 && isNormalizedWindowsDriveLetter(path[0])) {\n        return;\n    }\n    path.pop();\n}\nfunction includesCredentials(url) {\n    return url.username !== \"\" || url.password !== \"\";\n}\nfunction cannotHaveAUsernamePasswordPort(url) {\n    return url.host === null || url.host === \"\" || url.cannotBeABaseURL || url.scheme === \"file\";\n}\nfunction isNormalizedWindowsDriveLetter(string) {\n    return /^[A-Za-z]:$/.test(string);\n}\nfunction URLStateMachine(input, base, encodingOverride, url, stateOverride) {\n    this.pointer = 0;\n    this.input = input;\n    this.base = base || null;\n    this.encodingOverride = encodingOverride || \"utf-8\";\n    this.stateOverride = stateOverride;\n    this.url = url;\n    this.failure = false;\n    this.parseError = false;\n    if (!this.url) {\n        this.url = {\n            scheme: \"\",\n            username: \"\",\n            password: \"\",\n            host: null,\n            port: null,\n            path: [],\n            query: null,\n            fragment: null,\n            cannotBeABaseURL: false\n        };\n        const res = trimControlChars(this.input);\n        if (res !== this.input) {\n            this.parseError = true;\n        }\n        this.input = res;\n    }\n    const res = trimTabAndNewline(this.input);\n    if (res !== this.input) {\n        this.parseError = true;\n    }\n    this.input = res;\n    this.state = stateOverride || \"scheme start\";\n    this.buffer = \"\";\n    this.atFlag = false;\n    this.arrFlag = false;\n    this.passwordTokenSeenFlag = false;\n    this.input = punycode.ucs2.decode(this.input);\n    for(; this.pointer <= this.input.length; ++this.pointer){\n        const c = this.input[this.pointer];\n        const cStr = isNaN(c) ? undefined : String.fromCodePoint(c);\n        // exec state machine\n        const ret = this[\"parse \" + this.state](c, cStr);\n        if (!ret) {\n            break; // terminate algorithm\n        } else if (ret === failure) {\n            this.failure = true;\n            break;\n        }\n    }\n}\nURLStateMachine.prototype[\"parse scheme start\"] = function parseSchemeStart(c, cStr) {\n    if (isASCIIAlpha(c)) {\n        this.buffer += cStr.toLowerCase();\n        this.state = \"scheme\";\n    } else if (!this.stateOverride) {\n        this.state = \"no scheme\";\n        --this.pointer;\n    } else {\n        this.parseError = true;\n        return failure;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse scheme\"] = function parseScheme(c, cStr) {\n    if (isASCIIAlphanumeric(c) || c === 43 || c === 45 || c === 46) {\n        this.buffer += cStr.toLowerCase();\n    } else if (c === 58) {\n        if (this.stateOverride) {\n            if (isSpecial(this.url) && !isSpecialScheme(this.buffer)) {\n                return false;\n            }\n            if (!isSpecial(this.url) && isSpecialScheme(this.buffer)) {\n                return false;\n            }\n            if ((includesCredentials(this.url) || this.url.port !== null) && this.buffer === \"file\") {\n                return false;\n            }\n            if (this.url.scheme === \"file\" && (this.url.host === \"\" || this.url.host === null)) {\n                return false;\n            }\n        }\n        this.url.scheme = this.buffer;\n        this.buffer = \"\";\n        if (this.stateOverride) {\n            return false;\n        }\n        if (this.url.scheme === \"file\") {\n            if (this.input[this.pointer + 1] !== 47 || this.input[this.pointer + 2] !== 47) {\n                this.parseError = true;\n            }\n            this.state = \"file\";\n        } else if (isSpecial(this.url) && this.base !== null && this.base.scheme === this.url.scheme) {\n            this.state = \"special relative or authority\";\n        } else if (isSpecial(this.url)) {\n            this.state = \"special authority slashes\";\n        } else if (this.input[this.pointer + 1] === 47) {\n            this.state = \"path or authority\";\n            ++this.pointer;\n        } else {\n            this.url.cannotBeABaseURL = true;\n            this.url.path.push(\"\");\n            this.state = \"cannot-be-a-base-URL path\";\n        }\n    } else if (!this.stateOverride) {\n        this.buffer = \"\";\n        this.state = \"no scheme\";\n        this.pointer = -1;\n    } else {\n        this.parseError = true;\n        return failure;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse no scheme\"] = function parseNoScheme(c) {\n    if (this.base === null || this.base.cannotBeABaseURL && c !== 35) {\n        return failure;\n    } else if (this.base.cannotBeABaseURL && c === 35) {\n        this.url.scheme = this.base.scheme;\n        this.url.path = this.base.path.slice();\n        this.url.query = this.base.query;\n        this.url.fragment = \"\";\n        this.url.cannotBeABaseURL = true;\n        this.state = \"fragment\";\n    } else if (this.base.scheme === \"file\") {\n        this.state = \"file\";\n        --this.pointer;\n    } else {\n        this.state = \"relative\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse special relative or authority\"] = function parseSpecialRelativeOrAuthority(c) {\n    if (c === 47 && this.input[this.pointer + 1] === 47) {\n        this.state = \"special authority ignore slashes\";\n        ++this.pointer;\n    } else {\n        this.parseError = true;\n        this.state = \"relative\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse path or authority\"] = function parsePathOrAuthority(c) {\n    if (c === 47) {\n        this.state = \"authority\";\n    } else {\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse relative\"] = function parseRelative(c) {\n    this.url.scheme = this.base.scheme;\n    if (isNaN(c)) {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.url.path = this.base.path.slice();\n        this.url.query = this.base.query;\n    } else if (c === 47) {\n        this.state = \"relative slash\";\n    } else if (c === 63) {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.url.path = this.base.path.slice();\n        this.url.query = \"\";\n        this.state = \"query\";\n    } else if (c === 35) {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.url.path = this.base.path.slice();\n        this.url.query = this.base.query;\n        this.url.fragment = \"\";\n        this.state = \"fragment\";\n    } else if (isSpecial(this.url) && c === 92) {\n        this.parseError = true;\n        this.state = \"relative slash\";\n    } else {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.url.path = this.base.path.slice(0, this.base.path.length - 1);\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse relative slash\"] = function parseRelativeSlash(c) {\n    if (isSpecial(this.url) && (c === 47 || c === 92)) {\n        if (c === 92) {\n            this.parseError = true;\n        }\n        this.state = \"special authority ignore slashes\";\n    } else if (c === 47) {\n        this.state = \"authority\";\n    } else {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse special authority slashes\"] = function parseSpecialAuthoritySlashes(c) {\n    if (c === 47 && this.input[this.pointer + 1] === 47) {\n        this.state = \"special authority ignore slashes\";\n        ++this.pointer;\n    } else {\n        this.parseError = true;\n        this.state = \"special authority ignore slashes\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse special authority ignore slashes\"] = function parseSpecialAuthorityIgnoreSlashes(c) {\n    if (c !== 47 && c !== 92) {\n        this.state = \"authority\";\n        --this.pointer;\n    } else {\n        this.parseError = true;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse authority\"] = function parseAuthority(c, cStr) {\n    if (c === 64) {\n        this.parseError = true;\n        if (this.atFlag) {\n            this.buffer = \"%40\" + this.buffer;\n        }\n        this.atFlag = true;\n        // careful, this is based on buffer and has its own pointer (this.pointer != pointer) and inner chars\n        const len = countSymbols(this.buffer);\n        for(let pointer = 0; pointer < len; ++pointer){\n            const codePoint = this.buffer.codePointAt(pointer);\n            if (codePoint === 58 && !this.passwordTokenSeenFlag) {\n                this.passwordTokenSeenFlag = true;\n                continue;\n            }\n            const encodedCodePoints = percentEncodeChar(codePoint, isUserinfoPercentEncode);\n            if (this.passwordTokenSeenFlag) {\n                this.url.password += encodedCodePoints;\n            } else {\n                this.url.username += encodedCodePoints;\n            }\n        }\n        this.buffer = \"\";\n    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92) {\n        if (this.atFlag && this.buffer === \"\") {\n            this.parseError = true;\n            return failure;\n        }\n        this.pointer -= countSymbols(this.buffer) + 1;\n        this.buffer = \"\";\n        this.state = \"host\";\n    } else {\n        this.buffer += cStr;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse hostname\"] = URLStateMachine.prototype[\"parse host\"] = function parseHostName(c, cStr) {\n    if (this.stateOverride && this.url.scheme === \"file\") {\n        --this.pointer;\n        this.state = \"file host\";\n    } else if (c === 58 && !this.arrFlag) {\n        if (this.buffer === \"\") {\n            this.parseError = true;\n            return failure;\n        }\n        const host = parseHost(this.buffer, isSpecial(this.url));\n        if (host === failure) {\n            return failure;\n        }\n        this.url.host = host;\n        this.buffer = \"\";\n        this.state = \"port\";\n        if (this.stateOverride === \"hostname\") {\n            return false;\n        }\n    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92) {\n        --this.pointer;\n        if (isSpecial(this.url) && this.buffer === \"\") {\n            this.parseError = true;\n            return failure;\n        } else if (this.stateOverride && this.buffer === \"\" && (includesCredentials(this.url) || this.url.port !== null)) {\n            this.parseError = true;\n            return false;\n        }\n        const host = parseHost(this.buffer, isSpecial(this.url));\n        if (host === failure) {\n            return failure;\n        }\n        this.url.host = host;\n        this.buffer = \"\";\n        this.state = \"path start\";\n        if (this.stateOverride) {\n            return false;\n        }\n    } else {\n        if (c === 91) {\n            this.arrFlag = true;\n        } else if (c === 93) {\n            this.arrFlag = false;\n        }\n        this.buffer += cStr;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse port\"] = function parsePort(c, cStr) {\n    if (isASCIIDigit(c)) {\n        this.buffer += cStr;\n    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92 || this.stateOverride) {\n        if (this.buffer !== \"\") {\n            const port = parseInt(this.buffer);\n            if (port > Math.pow(2, 16) - 1) {\n                this.parseError = true;\n                return failure;\n            }\n            this.url.port = port === defaultPort(this.url.scheme) ? null : port;\n            this.buffer = \"\";\n        }\n        if (this.stateOverride) {\n            return false;\n        }\n        this.state = \"path start\";\n        --this.pointer;\n    } else {\n        this.parseError = true;\n        return failure;\n    }\n    return true;\n};\nconst fileOtherwiseCodePoints = new Set([\n    47,\n    92,\n    63,\n    35\n]);\nURLStateMachine.prototype[\"parse file\"] = function parseFile(c) {\n    this.url.scheme = \"file\";\n    if (c === 47 || c === 92) {\n        if (c === 92) {\n            this.parseError = true;\n        }\n        this.state = \"file slash\";\n    } else if (this.base !== null && this.base.scheme === \"file\") {\n        if (isNaN(c)) {\n            this.url.host = this.base.host;\n            this.url.path = this.base.path.slice();\n            this.url.query = this.base.query;\n        } else if (c === 63) {\n            this.url.host = this.base.host;\n            this.url.path = this.base.path.slice();\n            this.url.query = \"\";\n            this.state = \"query\";\n        } else if (c === 35) {\n            this.url.host = this.base.host;\n            this.url.path = this.base.path.slice();\n            this.url.query = this.base.query;\n            this.url.fragment = \"\";\n            this.state = \"fragment\";\n        } else {\n            if (this.input.length - this.pointer - 1 === 0 || // remaining consists of 0 code points\n            !isWindowsDriveLetterCodePoints(c, this.input[this.pointer + 1]) || this.input.length - this.pointer - 1 >= 2 && // remaining has at least 2 code points\n            !fileOtherwiseCodePoints.has(this.input[this.pointer + 2])) {\n                this.url.host = this.base.host;\n                this.url.path = this.base.path.slice();\n                shortenPath(this.url);\n            } else {\n                this.parseError = true;\n            }\n            this.state = \"path\";\n            --this.pointer;\n        }\n    } else {\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse file slash\"] = function parseFileSlash(c) {\n    if (c === 47 || c === 92) {\n        if (c === 92) {\n            this.parseError = true;\n        }\n        this.state = \"file host\";\n    } else {\n        if (this.base !== null && this.base.scheme === \"file\") {\n            if (isNormalizedWindowsDriveLetterString(this.base.path[0])) {\n                this.url.path.push(this.base.path[0]);\n            } else {\n                this.url.host = this.base.host;\n            }\n        }\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse file host\"] = function parseFileHost(c, cStr) {\n    if (isNaN(c) || c === 47 || c === 92 || c === 63 || c === 35) {\n        --this.pointer;\n        if (!this.stateOverride && isWindowsDriveLetterString(this.buffer)) {\n            this.parseError = true;\n            this.state = \"path\";\n        } else if (this.buffer === \"\") {\n            this.url.host = \"\";\n            if (this.stateOverride) {\n                return false;\n            }\n            this.state = \"path start\";\n        } else {\n            let host = parseHost(this.buffer, isSpecial(this.url));\n            if (host === failure) {\n                return failure;\n            }\n            if (host === \"localhost\") {\n                host = \"\";\n            }\n            this.url.host = host;\n            if (this.stateOverride) {\n                return false;\n            }\n            this.buffer = \"\";\n            this.state = \"path start\";\n        }\n    } else {\n        this.buffer += cStr;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse path start\"] = function parsePathStart(c) {\n    if (isSpecial(this.url)) {\n        if (c === 92) {\n            this.parseError = true;\n        }\n        this.state = \"path\";\n        if (c !== 47 && c !== 92) {\n            --this.pointer;\n        }\n    } else if (!this.stateOverride && c === 63) {\n        this.url.query = \"\";\n        this.state = \"query\";\n    } else if (!this.stateOverride && c === 35) {\n        this.url.fragment = \"\";\n        this.state = \"fragment\";\n    } else if (c !== undefined) {\n        this.state = \"path\";\n        if (c !== 47) {\n            --this.pointer;\n        }\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse path\"] = function parsePath(c) {\n    if (isNaN(c) || c === 47 || isSpecial(this.url) && c === 92 || !this.stateOverride && (c === 63 || c === 35)) {\n        if (isSpecial(this.url) && c === 92) {\n            this.parseError = true;\n        }\n        if (isDoubleDot(this.buffer)) {\n            shortenPath(this.url);\n            if (c !== 47 && !(isSpecial(this.url) && c === 92)) {\n                this.url.path.push(\"\");\n            }\n        } else if (isSingleDot(this.buffer) && c !== 47 && !(isSpecial(this.url) && c === 92)) {\n            this.url.path.push(\"\");\n        } else if (!isSingleDot(this.buffer)) {\n            if (this.url.scheme === \"file\" && this.url.path.length === 0 && isWindowsDriveLetterString(this.buffer)) {\n                if (this.url.host !== \"\" && this.url.host !== null) {\n                    this.parseError = true;\n                    this.url.host = \"\";\n                }\n                this.buffer = this.buffer[0] + \":\";\n            }\n            this.url.path.push(this.buffer);\n        }\n        this.buffer = \"\";\n        if (this.url.scheme === \"file\" && (c === undefined || c === 63 || c === 35)) {\n            while(this.url.path.length > 1 && this.url.path[0] === \"\"){\n                this.parseError = true;\n                this.url.path.shift();\n            }\n        }\n        if (c === 63) {\n            this.url.query = \"\";\n            this.state = \"query\";\n        }\n        if (c === 35) {\n            this.url.fragment = \"\";\n            this.state = \"fragment\";\n        }\n    } else {\n        // TODO: If c is not a URL code point and not \"%\", parse error.\n        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {\n            this.parseError = true;\n        }\n        this.buffer += percentEncodeChar(c, isPathPercentEncode);\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse cannot-be-a-base-URL path\"] = function parseCannotBeABaseURLPath(c) {\n    if (c === 63) {\n        this.url.query = \"\";\n        this.state = \"query\";\n    } else if (c === 35) {\n        this.url.fragment = \"\";\n        this.state = \"fragment\";\n    } else {\n        // TODO: Add: not a URL code point\n        if (!isNaN(c) && c !== 37) {\n            this.parseError = true;\n        }\n        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {\n            this.parseError = true;\n        }\n        if (!isNaN(c)) {\n            this.url.path[0] = this.url.path[0] + percentEncodeChar(c, isC0ControlPercentEncode);\n        }\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse query\"] = function parseQuery(c, cStr) {\n    if (isNaN(c) || !this.stateOverride && c === 35) {\n        if (!isSpecial(this.url) || this.url.scheme === \"ws\" || this.url.scheme === \"wss\") {\n            this.encodingOverride = \"utf-8\";\n        }\n        const buffer = new Buffer(this.buffer); // TODO: Use encoding override instead\n        for(let i = 0; i < buffer.length; ++i){\n            if (buffer[i] < 0x21 || buffer[i] > 0x7E || buffer[i] === 0x22 || buffer[i] === 0x23 || buffer[i] === 0x3C || buffer[i] === 0x3E) {\n                this.url.query += percentEncode(buffer[i]);\n            } else {\n                this.url.query += String.fromCodePoint(buffer[i]);\n            }\n        }\n        this.buffer = \"\";\n        if (c === 35) {\n            this.url.fragment = \"\";\n            this.state = \"fragment\";\n        }\n    } else {\n        // TODO: If c is not a URL code point and not \"%\", parse error.\n        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {\n            this.parseError = true;\n        }\n        this.buffer += cStr;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse fragment\"] = function parseFragment(c) {\n    if (isNaN(c)) {} else if (c === 0x0) {\n        this.parseError = true;\n    } else {\n        // TODO: If c is not a URL code point and not \"%\", parse error.\n        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {\n            this.parseError = true;\n        }\n        this.url.fragment += percentEncodeChar(c, isC0ControlPercentEncode);\n    }\n    return true;\n};\nfunction serializeURL(url, excludeFragment) {\n    let output = url.scheme + \":\";\n    if (url.host !== null) {\n        output += \"//\";\n        if (url.username !== \"\" || url.password !== \"\") {\n            output += url.username;\n            if (url.password !== \"\") {\n                output += \":\" + url.password;\n            }\n            output += \"@\";\n        }\n        output += serializeHost(url.host);\n        if (url.port !== null) {\n            output += \":\" + url.port;\n        }\n    } else if (url.host === null && url.scheme === \"file\") {\n        output += \"//\";\n    }\n    if (url.cannotBeABaseURL) {\n        output += url.path[0];\n    } else {\n        for (const string of url.path){\n            output += \"/\" + string;\n        }\n    }\n    if (url.query !== null) {\n        output += \"?\" + url.query;\n    }\n    if (!excludeFragment && url.fragment !== null) {\n        output += \"#\" + url.fragment;\n    }\n    return output;\n}\nfunction serializeOrigin(tuple) {\n    let result = tuple.scheme + \"://\";\n    result += serializeHost(tuple.host);\n    if (tuple.port !== null) {\n        result += \":\" + tuple.port;\n    }\n    return result;\n}\nmodule.exports.serializeURL = serializeURL;\nmodule.exports.serializeURLOrigin = function(url) {\n    // https://url.spec.whatwg.org/#concept-url-origin\n    switch(url.scheme){\n        case \"blob\":\n            try {\n                return module.exports.serializeURLOrigin(module.exports.parseURL(url.path[0]));\n            } catch (e) {\n                // serializing an opaque origin returns \"null\"\n                return \"null\";\n            }\n        case \"ftp\":\n        case \"gopher\":\n        case \"http\":\n        case \"https\":\n        case \"ws\":\n        case \"wss\":\n            return serializeOrigin({\n                scheme: url.scheme,\n                host: url.host,\n                port: url.port\n            });\n        case \"file\":\n            // spec says \"exercise to the reader\", chrome says \"file://\"\n            return \"file://\";\n        default:\n            // serializing an opaque origin returns \"null\"\n            return \"null\";\n    }\n};\nmodule.exports.basicURLParse = function(input, options) {\n    if (options === undefined) {\n        options = {};\n    }\n    const usm = new URLStateMachine(input, options.baseURL, options.encodingOverride, options.url, options.stateOverride);\n    if (usm.failure) {\n        return \"failure\";\n    }\n    return usm.url;\n};\nmodule.exports.setTheUsername = function(url, username) {\n    url.username = \"\";\n    const decoded = punycode.ucs2.decode(username);\n    for(let i = 0; i < decoded.length; ++i){\n        url.username += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\n    }\n};\nmodule.exports.setThePassword = function(url, password) {\n    url.password = \"\";\n    const decoded = punycode.ucs2.decode(password);\n    for(let i = 0; i < decoded.length; ++i){\n        url.password += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\n    }\n};\nmodule.exports.serializeHost = serializeHost;\nmodule.exports.cannotHaveAUsernamePasswordPort = cannotHaveAUsernamePasswordPort;\nmodule.exports.serializeInteger = function(integer) {\n    return String(integer);\n};\nmodule.exports.parseURL = function(input, options) {\n    if (options === undefined) {\n        options = {};\n    }\n    // We don't handle blobs, so this just delegates:\n    return module.exports.basicURLParse(input, {\n        baseURL: options.baseURL,\n        encodingOverride: options.encodingOverride\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/whatwg-url/lib/utils.js":
/*!**********************************************!*\
  !*** ./node_modules/whatwg-url/lib/utils.js ***!
  \**********************************************/
/***/ ((module) => {

eval("\nmodule.exports.mixin = function mixin(target, source) {\n    const keys = Object.getOwnPropertyNames(source);\n    for(let i = 0; i < keys.length; ++i){\n        Object.defineProperty(target, keys[i], Object.getOwnPropertyDescriptor(source, keys[i]));\n    }\n};\nmodule.exports.wrapperSymbol = Symbol(\"wrapper\");\nmodule.exports.implSymbol = Symbol(\"impl\");\nmodule.exports.wrapperForImpl = function(impl) {\n    return impl[module.exports.wrapperSymbol];\n};\nmodule.exports.implForWrapper = function(wrapper) {\n    return wrapper[module.exports.implSymbol];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/utils.js\n");

/***/ })

};
;