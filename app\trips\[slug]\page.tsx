'use client';

import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Star,
  Camera,
  Mountain,
  Waves,
  TreePine,
  ArrowLeft,
  Phone,
  Mail,
  Share2,
  Heart,
  Download
} from 'lucide-react'
import Button from '@/components/ui/Button'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { TripImageGallery } from '@/components/trips/TripImageGallery'
import { TripItinerary } from '@/components/trips/TripItinerary'
import { BookingForm } from '@/components/trips/BookingForm'
import { RelatedTrips } from '@/components/trips/RelatedTrips'

// Sample trip data based on scraped content from positive7.in
const SAMPLE_TRIPS = {
  'manali-students-tour': {
    id: 'manali-students-tour',
    title: 'Blissful Manali Students Tour',
    subtitle: "Let's get lost in the mountain, And make our soul happy!",
    description: 'One of the most popular hill stations in Himachal, Manali offers the most magnificent views of the Pir Panjal and the Dhauladhar ranges covered with snow for most parts of the year. <PERSON><PERSON> has many trekking options around it, making it a great base for exploring this side of Himalayas.',
    duration: '9 Days 8 Nights',
    price: 27500,
    difficulty: 'Moderate',
    category: 'Adventure',
    rating: 4.8,
    reviewCount: 156,
    maxParticipants: 40,
    minAge: 12,
    images: [
      'https://positive7.in/wp-content/uploads/2025/01/gettyimages-1134041601-612x612-1.jpg',
      'https://positive7.in/wp-content/uploads/2022/07/Manali-River.jpg',
      'https://positive7.in/wp-content/uploads/2022/07/Manali-2.jpg',
      'https://positive7.in/wp-content/uploads/2022/07/Manali-para-768x576.jpg'
    ],
    highlights: [
      'Trek to Jogini Fall & Parsha Falls',
      'Snow Activities with Snow Dress',
      'River Rafting in Kullu',
      'Visit to Solang Valley & Atal Tunnel',
      'Bonfire & Music Night',
      'Rock Garden & Rose Garden in Chandigarh'
    ],
    inclusions: [
      '3rd AC train fare from Ahmedabad to Delhi',
      'Local Travel in AC buses',
      'Accommodation on quad sharing basis',
      'All Meals (Breakfast, Lunch, Dinner)',
      'River Rafting & Snow activities',
      'Travel Insurance',
      'Professional guides and supervision'
    ],
    exclusions: [
      'Personal expenses (laundry, tips, etc.)',
      'Mineral/soft drinks',
      'Any cost due to natural calamities',
      'Items not mentioned in inclusions'
    ],
    itinerary: [
      {
        day: 1,
        title: 'Departure from Ahmedabad',
        description: 'Report at Ahmedabad Railway station to board our train to Delhi. Night will be in Train.',
        activities: ['Train departure', 'Overnight journey'],
        meals: ['Dinner']
      },
      {
        day: 2,
        title: 'Delhi to Chandigarh',
        description: 'Reach Delhi in the morning and get transferred to Chandigarh. Sightseeing covering Rock Garden, Rose Garden & Sukhmana Lake.',
        activities: ['Rock Garden visit', 'Rose Garden', 'Sukhmana Lake'],
        meals: ['Breakfast', 'Lunch', 'Dinner']
      },
      {
        day: 3,
        title: 'Chandigarh to Manali',
        description: 'Post breakfast Check-out and get transferred to Manali. Expected to reach Manali by late afternoon.',
        activities: ['Scenic drive to Manali', 'Check-in to resort'],
        meals: ['Breakfast', 'Lunch', 'Dinner']
      },
      {
        day: 4,
        title: 'Jogini & Parsha Waterfall Trek',
        description: 'Adventure trekking to Jogini Fall through apple orchards, tall pine trees & small water streams. Visit Parsha waterfall.',
        activities: ['Morning river walk', 'Jogini Fall trek', 'Parsha Falls visit', 'Yogini Mata temple'],
        meals: ['Breakfast', 'Lunch', 'Dinner']
      },
      {
        day: 5,
        title: 'Solang Valley Adventure',
        description: 'Adventure trip to Solang valley, Atal tunnel and Sissu for snow activities. Visit Hidimba Temple.',
        activities: ['Solang Valley', 'Atal Tunnel', 'Snow activities', 'Hidimba Temple'],
        meals: ['Breakfast', 'Lunch', 'Dinner']
      },
      {
        day: 6,
        title: 'River Rafting & Local Exploration',
        description: 'Thrilling River Rafting session in Kullu. Visit Tibetan Monastery and local market. Bonfire night.',
        activities: ['River rafting', 'Tibetan Monastery', 'Local market', 'Bonfire & music'],
        meals: ['Breakfast', 'Lunch', 'Dinner']
      },
      {
        day: 7,
        title: 'Return to Chandigarh',
        description: 'Checkout and get transferred to Chandigarh. Evening explore the local market.',
        activities: ['Travel to Chandigarh', 'Local market exploration'],
        meals: ['Breakfast', 'Lunch', 'Dinner']
      },
      {
        day: 8,
        title: 'Delhi to Ahmedabad',
        description: 'Transfer to Delhi railway station to board train to Ahmedabad. Night journey.',
        activities: ['Train departure', 'Overnight journey'],
        meals: ['Breakfast', 'Lunch', 'Dinner']
      },
      {
        day: 9,
        title: 'Arrival in Ahmedabad',
        description: 'Reach Ahmedabad in the morning. Trip ends with fond memories to cherish.',
        activities: ['Arrival', 'Trip conclusion'],
        meals: ['Breakfast']
      }
    ],
    dates: [
      { start: '2024-02-15', end: '2024-02-23', available: true },
      { start: '2024-02-18', end: '2024-02-26', available: true },
      { start: '2024-03-11', end: '2024-03-19', available: true },
      { start: '2024-04-07', end: '2024-04-15', available: false },
      { start: '2024-05-02', end: '2024-05-10', available: true }
    ]
  }
}

interface TripDetailPageProps {
  params: {
    slug: string
  }
}

export default function TripDetailPage({ params }: TripDetailPageProps) {
  const trip = SAMPLE_TRIPS[params.slug as keyof typeof SAMPLE_TRIPS]

  if (!trip) {
    notFound()
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50"
    >
      {/* Hero Section */}
      <motion.section variants={itemVariants} className="relative h-[70vh] overflow-hidden">
        <Image
          src={trip.images[0]}
          alt={trip.title}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />

        {/* Navigation */}
        <div className="absolute top-6 left-6 z-10">
          <Link href="/trips">
            <Button variant="secondary" size="sm" className="bg-white/20 backdrop-blur-sm hover:bg-white/30">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Trips
            </Button>
          </Link>
        </div>

        {/* Action Buttons */}
        <div className="absolute top-6 right-6 z-10 flex gap-2">
          <Button variant="secondary" size="sm" className="bg-white/20 backdrop-blur-sm hover:bg-white/30">
            <Heart className="w-4 h-4" />
          </Button>
          <Button variant="secondary" size="sm" className="bg-white/20 backdrop-blur-sm hover:bg-white/30">
            <Share2 className="w-4 h-4" />
          </Button>
        </div>

        {/* Hero Content */}
        <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
          <div className="max-w-7xl mx-auto">
            <motion.div
              variants={itemVariants}
              className="flex flex-wrap items-end justify-between gap-6"
            >
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-4 mb-4">
                  <span className="px-3 py-1 bg-blue-600 rounded-full text-sm font-medium">
                    {trip.category}
                  </span>
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{trip.rating}</span>
                    <span className="text-white/80">({trip.reviewCount} reviews)</span>
                  </div>
                </div>
                <h1 className="text-4xl md:text-6xl font-bold mb-2">{trip.title}</h1>
                <p className="text-xl text-white/90 mb-4">{trip.subtitle}</p>
                <div className="flex flex-wrap items-center gap-6 text-white/80">
                  <div className="flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    <span>{trip.duration}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    <span>Max {trip.maxParticipants} participants</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mountain className="w-5 h-5" />
                    <span>{trip.difficulty}</span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold mb-2">₹{trip.price.toLocaleString()}</div>
                <div className="text-white/80 mb-4">per person</div>
                <Button size="lg" className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                  Book Now
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-12">
            {/* Trip Overview */}
            <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
              <h2 className="text-3xl font-bold mb-6 text-gray-900">Trip Overview</h2>
              <p className="text-gray-700 text-lg leading-relaxed mb-8">{trip.description}</p>

              {/* Trip Highlights */}
              <div className="mb-8">
                <h3 className="text-xl font-semibold mb-4 text-gray-900">Trip Highlights</h3>
                <div className="grid md:grid-cols-2 gap-3">
                  {trip.highlights.map((highlight, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-600 to-green-600 rounded-full" />
                      <span className="text-gray-700">{highlight}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Quick Info Grid */}
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Clock className="w-5 h-5 text-blue-600" />
                    <div>
                      <div className="font-medium text-gray-900">Duration</div>
                      <div className="text-gray-600">{trip.duration}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Users className="w-5 h-5 text-blue-600" />
                    <div>
                      <div className="font-medium text-gray-900">Group Size</div>
                      <div className="text-gray-600">Max {trip.maxParticipants} participants</div>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Mountain className="w-5 h-5 text-blue-600" />
                    <div>
                      <div className="font-medium text-gray-900">Difficulty</div>
                      <div className="text-gray-600">{trip.difficulty}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Calendar className="w-5 h-5 text-blue-600" />
                    <div>
                      <div className="font-medium text-gray-900">Min Age</div>
                      <div className="text-gray-600">{trip.minAge}+ years</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.section>

            {/* Image Gallery */}
            <motion.section variants={itemVariants}>
              <TripImageGallery images={trip.images} title={trip.title} />
            </motion.section>

            {/* Itinerary */}
            <motion.section variants={itemVariants}>
              <TripItinerary itinerary={trip.itinerary} />
            </motion.section>

            {/* Inclusions & Exclusions */}
            <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
              <h2 className="text-3xl font-bold mb-8 text-gray-900">What's Included</h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-green-700 flex items-center gap-2">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-green-600 rounded-full" />
                    </div>
                    Included
                  </h3>
                  <ul className="space-y-3">
                    {trip.inclusions.map((item, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                          <div className="w-2 h-2 bg-green-600 rounded-full" />
                        </div>
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-red-700 flex items-center gap-2">
                    <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-red-600 rounded-full" />
                    </div>
                    Not Included
                  </h3>
                  <ul className="space-y-3">
                    {trip.exclusions.map((item, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div className="w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5">
                          <div className="w-2 h-2 bg-red-600 rounded-full" />
                        </div>
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.section>
          </div>

          {/* Right Column - Booking Sidebar */}
          <div className="lg:col-span-1">
            <motion.div variants={itemVariants} className="sticky top-24">
              <BookingForm trip={trip} />
            </motion.div>
          </div>
        </div>

        {/* Related Trips */}
        <motion.section variants={itemVariants} className="mt-16">
          <RelatedTrips currentTripId={trip.id} category={trip.category} />
        </motion.section>
      </div>
    </motion.div>
  )
}
