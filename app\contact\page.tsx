import { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { ContactHero } from '@/components/contact/ContactHero'
import { ContactForm } from '@/components/contact/ContactForm'
import { ContactInfo } from '@/components/contact/ContactInfo'
import { LocationMap } from '@/components/contact/LocationMap'
import { FAQSection } from '@/components/contact/FAQSection'

export const metadata: Metadata = {
  title: 'Contact Us - Positive7 Educational Tours',
  description: 'Get in touch with Positive7 for educational tour inquiries, bookings, and support. We\'re here to help plan your perfect learning adventure.',
  keywords: 'contact positive7, educational tour inquiry, booking support, Ahmedabad office, student travel help',
  openGraph: {
    title: 'Contact Positive7 - Let\'s Plan Your Educational Adventure',
    description: 'Ready to create unforgettable learning experiences? Contact our expert team for personalized educational tour planning.',
    images: ['/images/contact-hero.jpg'],
    type: 'website'
  }
}

export default function ContactPage() {
  return (
    <>
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Hero Section */}
      <ContactHero />

      {/* Main Contact Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <ContactForm />

            {/* Contact Information */}
            <ContactInfo />
          </div>
        </div>
      </section>

      {/* Location & Map */}
      <LocationMap />

      {/* FAQ Section */}
      <FAQSection />
        </div>
      </main>
      <Footer />
    </>
  )
}
