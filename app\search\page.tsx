import { Metadata } from 'next'
import { Suspense } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import SearchResults from '@/components/search/SearchResults'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export const metadata: Metadata = {
  title: 'Search Results - Positive7 Educational Tours',
  description: 'Find the perfect educational tour for your group. Search through our collection of amazing destinations and experiences.',
  keywords: 'search tours, educational trips, student tours, group travel, Positive7, Ahmedabad'
}

export default function SearchPage() {
  return (
    <>
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <Suspense fallback={
          <div className="flex items-center justify-center min-h-[400px]">
            <LoadingSpinner size="lg" />
          </div>
        }>
          <SearchResults />
        </Suspense>
      </div>
        </div>
      </main>
      <Footer />
    </>
  )
}
